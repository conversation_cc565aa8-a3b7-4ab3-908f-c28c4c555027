<template>
  <section class="cert-subject-container">
    <!-- 头部操作区 -->
    <div class="header-section">
      <div class="section-header">
        <h2 class="section-title">凭证科目管理</h2>
        <div class="header-actions">
          <el-button type="primary" size="medium" icon="el-icon-plus" @click="showBatchSelectDialog">
            批量选择帐套
          </el-button>
          <el-button type="success" size="medium" icon="el-icon-refresh" @click="loadData">
            刷新数据
          </el-button>
        </div>
      </div>

      <!-- 模板选择器 -->
      <div class="template-selector">
        <span class="selector-label">选择模板：</span>
        <el-select v-model="selectedTemplateId" placeholder="请选择模板" @change="onTemplateChange" clearable>
          <el-option
            v-for="template in templateList"
            :key="template.id"
            :label="template.name"
            :value="template.id">
            <span style="float: left">{{ template.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ template.code }}</span>
          </el-option>
        </el-select>
      </div>
    </div>

    <!-- 主内容区 -->
    <div v-if="selectedTemplateId" class="content-section">
      <!-- 税务核算分类 -->
      <div class="accounting-category">
        <div class="category-header">
          <h3 class="category-title">
            <i class="el-icon-document"></i>
            税务核算
          </h3>
          <el-button type="primary" size="small" @click="addCompanySubject(1)">
            添加公司科目
          </el-button>
        </div>

        <div class="company-subjects-grid">
          <div
            v-for="company in getTaxCompanies"
            :key="`tax-${company.companyId}`"
            class="company-subject-card"
          >
            <div class="company-card-header">
              <h4 class="company-name">{{ company.companyName }}</h4>
              <div class="company-actions">
                <el-button type="text" size="mini" @click="editCompanySubjects(company, 1)">
                  编辑科目
                </el-button>
                <el-button type="text" size="mini" style="color: #f56c6c;" @click="removeCompanySubjects(company, 1)">
                  移除
                </el-button>
              </div>
            </div>
            <div class="subjects-list">
              <div
                v-for="subject in getCompanySubjects(company.companyId, 1)"
                :key="subject.id"
                class="subject-item"
              >
                <span class="subject-name">{{ getSubjectName(subject.relationId) }}</span>
              </div>
              <div v-if="getCompanySubjects(company.companyId, 1).length === 0" class="no-subjects">
                暂无科目
              </div>
            </div>
          </div>

          <div v-if="getTaxCompanies.length === 0" class="empty-category">
            <el-empty description="暂无税务核算公司" />
          </div>
        </div>
      </div>

      <!-- 股东核算分类 -->
      <div class="accounting-category">
        <div class="category-header">
          <h3 class="category-title">
            <i class="el-icon-user"></i>
            股东核算
          </h3>
          <el-button type="primary" size="small" @click="addCompanySubject(2)">
            添加公司科目
          </el-button>
        </div>

        <div class="company-subjects-grid">
          <div
            v-for="company in getShareholderCompanies"
            :key="`shareholder-${company.companyId}`"
            class="company-subject-card"
          >
            <div class="company-card-header">
              <h4 class="company-name">{{ company.companyName }}</h4>
              <div class="company-actions">
                <el-button type="text" size="mini" @click="editCompanySubjects(company, 2)">
                  编辑科目
                </el-button>
                <el-button type="text" size="mini" style="color: #f56c6c;" @click="removeCompanySubjects(company, 2)">
                  移除
                </el-button>
              </div>
            </div>
            <div class="subjects-list">
              <div
                v-for="subject in getCompanySubjects(company.companyId, 2)"
                :key="subject.id"
                class="subject-item"
              >
                <span class="subject-name">{{ getSubjectName(subject.relationId) }}</span>
              </div>
              <div v-if="getCompanySubjects(company.companyId, 2).length === 0" class="no-subjects">
                暂无科目
              </div>
            </div>
          </div>

          <div v-if="getShareholderCompanies.length === 0" class="empty-category">
            <el-empty description="暂无股东核算公司" />
          </div>
        </div>
      </div>
    </div>

    <!-- 未选择模板时的提示 -->
    <div v-else class="no-template-selected">
      <el-empty description="请先选择一个模板" />
    </div>

    <!-- 批量选择帐套对话框 -->
    <el-dialog
      title="批量选择帐套"
      :visible.sync="batchSelectDialogVisible"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div class="batch-select-content">
        <div class="select-header">
          <div class="template-info">
            <span class="info-label">当前模板：</span>
            <span class="info-value">{{ getCurrentTemplateName() }}</span>
          </div>
          <div class="batch-actions">
            <el-button size="small" @click="selectAllAccounts">全选</el-button>
            <el-button size="small" @click="clearAllAccounts">清空</el-button>
          </div>
        </div>

        <div class="accounts-selection">
          <div class="selection-section">
            <h4>税务核算帐套</h4>
            <div class="accounts-grid">
              <el-checkbox
                v-for="account in taxAccounts"
                :key="`tax-${account.id}`"
                v-model="account.selected"
                class="account-checkbox"
              >
                <div class="account-info">
                  <div class="account-name">{{ account.companyName }}</div>
                  <div class="account-detail">{{ account.appName }} ({{ account.appCode }})</div>
                </div>
              </el-checkbox>
            </div>
          </div>

          <div class="selection-section">
            <h4>股东核算帐套</h4>
            <div class="accounts-grid">
              <el-checkbox
                v-for="account in shareholderAccounts"
                :key="`shareholder-${account.id}`"
                v-model="account.selected"
                class="account-checkbox"
              >
                <div class="account-info">
                  <div class="account-name">{{ account.companyName }}</div>
                  <div class="account-detail">{{ account.appName }} ({{ account.appCode }})</div>
                </div>
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="batchSelectDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveBatchSelection" :loading="saving">
          保存选择
        </el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import {
  getCertTemplList,
  getCertTemplTempList,
  getTokenCompanyList,
  removeCertTemplTemp,
  saveCertTemplTemp
} from '@/api/system/baseInit'

export default {
  name: 'CertSubject',
  data() {
    return {
      // 基础数据
      templateList: [],
      tokenCompanyList: [],
      certTemplTempList: [],
      selectedTemplateId: null,

      // 批量选择对话框
      batchSelectDialogVisible: false,
      taxAccounts: [],
      shareholderAccounts: [],

      // 状态
      loading: false,
      saving: false
    }
  },
  computed: {
    // 获取税务核算的公司列表
    getTaxCompanies() {
      const companyIds = [...new Set(
        this.certTemplTempList
          .filter(item => item.templ_id === this.selectedTemplateId && item.acc_model === 1)
          .map(item => item.company_id)
      )]

      return companyIds.map(companyId => {
        const tokenCompany = this.tokenCompanyList.find(tc => tc.companyId === companyId)
        return tokenCompany ? {
          companyId: tokenCompany.companyId,
          companyName: tokenCompany.companyName
        } : null
      }).filter(Boolean)
    },

    // 获取股东核算的公司列表
    getShareholderCompanies() {
      const companyIds = [...new Set(
        this.certTemplTempList
          .filter(item => item.templ_id === this.selectedTemplateId && item.acc_model === 2)
          .map(item => item.company_id)
      )]

      return companyIds.map(companyId => {
        const tokenCompany = this.tokenCompanyList.find(tc => tc.companyId === companyId)
        return tokenCompany ? {
          companyId: tokenCompany.companyId,
          companyName: tokenCompany.companyName
        } : null
      }).filter(Boolean)
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    // 加载所有数据
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadTemplateList(),
          this.loadTokenCompanyList(),
          this.loadCertTemplTempList()
        ])
      } catch (error) {
        this.$message.error('数据加载失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    // 加载模板列表
    loadTemplateList() {
      return getCertTemplList().then(res => {
        this.templateList = res.list || res || []
      })
    },

    // 加载公司帐套列表
    loadTokenCompanyList() {
      return getTokenCompanyList().then(res => {
        this.tokenCompanyList = res.list || res || []
      })
    },

    // 加载模板帐套绑定关系
    loadCertTemplTempList() {
      return getCertTemplTempList().then(res => {
        this.certTemplTempList = res.list || res || []
      })
    },

    // 模板选择变化
    onTemplateChange() {
      // 重新准备批量选择数据
      this.prepareBatchSelectData()
    },

    // 准备批量选择数据
    prepareBatchSelectData() {
      if (!this.selectedTemplateId) return

      // 获取当前模板已选择的帐套
      const selectedAccountIds = this.certTemplTempList
        .filter(item => item.templId === this.selectedTemplateId)
        .map(item => `${item.companyId}-${item.accModel}`)

      // 准备税务核算帐套数据
      this.taxAccounts = this.tokenCompanyList
        .filter(account => account.accModel === 1)
        .map(account => ({
          ...account,
          selected: selectedAccountIds.includes(`${account.companyId}-1`)
        }))

      // 准备股东核算帐套数据
      this.shareholderAccounts = this.tokenCompanyList
        .filter(account => account.accModel === 2)
        .map(account => ({
          ...account,
          selected: selectedAccountIds.includes(`${account.companyId}-2`)
        }))
    },

    // 获取指定公司和核算类型的科目列表
    getCompanySubjects(companyId, accModel) {
      return this.certTemplTempList.filter(item =>
        item.templId === this.selectedTemplateId &&
        item.companyId === companyId &&
        item.accModel === accModel
      )
    },

    // 获取科目名称（这里需要根据relationId获取实际的科目名称）
    getSubjectName(relationId) {
      // 这里应该根据relationId从科目表中获取科目名称
      // 暂时返回ID，实际应该调用相关API获取科目详情
      return `科目-${relationId}`
    },

    // 获取当前模板名称
    getCurrentTemplateName() {
      const template = this.templateList.find(t => t.id === this.selectedTemplateId)
      return template ? template.name : '未选择'
    },

    // 显示批量选择对话框
    showBatchSelectDialog() {
      if (!this.selectedTemplateId) {
        this.$message.warning('请先选择一个模板')
        return
      }
      this.prepareBatchSelectData()
      this.batchSelectDialogVisible = true
    },

    // 全选帐套
    selectAllAccounts() {
      this.taxAccounts.forEach(account => {
        account.selected = true
      })
      this.shareholderAccounts.forEach(account => {
        account.selected = true
      })
    },

    // 清空选择
    clearAllAccounts() {
      this.taxAccounts.forEach(account => {
        account.selected = false
      })
      this.shareholderAccounts.forEach(account => {
        account.selected = false
      })
    },

    // 保存批量选择
    async saveBatchSelection() {
      if (!this.selectedTemplateId) {
        this.$message.warning('请先选择模板')
        return
      }

      this.saving = true
      try {
        // 获取当前选中的帐套
        const selectedAccounts = [
          ...this.taxAccounts.filter(account => account.selected),
          ...this.shareholderAccounts.filter(account => account.selected)
        ]

        // 获取当前模板的所有绑定关系
        const currentBindings = this.certTemplTempList.filter(item =>
          item.templId === this.selectedTemplateId
        )

        // 需要删除的绑定关系
        const toRemove = currentBindings.filter(binding => {
          const accountKey = `${binding.companyId}-${binding.accModel}`
          return !selectedAccounts.some(account =>
            `${account.companyId}-${account.accModel}` === accountKey
          )
        })

        // 需要添加的绑定关系
        const toAdd = selectedAccounts.filter(account => {
          const accountKey = `${account.companyId}-${account.accModel}`
          return !currentBindings.some(binding =>
            `${binding.companyId}-${binding.accModel}` === accountKey
          )
        })

        // 执行删除操作
        if (toRemove.length > 0) {
          await removeCertTemplTemp({
            templId: this.selectedTemplateId,
            items: toRemove.map(item => ({
              companyId: item.companyId,
              accModel: item.accModel
            }))
          })
        }

        // 执行添加操作
        if (toAdd.length > 0) {
          // 只为帐套创建模板级别的绑定关系，不自动绑定到具体会计科目
          const addData = []
          toAdd.forEach(account => {
            // 创建模板级别的绑定，relationId 设为 0 表示未绑定具体科目
            addData.push({
              companyId: account.companyId,
              accModel: account.accModel,
              templId: this.selectedTemplateId,
              relationId: 0 // 0 表示模板级别绑定，未绑定具体科目
            })
          })

          await saveCertTemplTemp(addData)
        }

        this.$message.success('保存成功')
        this.batchSelectDialogVisible = false
        await this.loadCertTemplTempList()
      } catch (error) {
        this.$message.error('保存失败')
        console.error(error)
      } finally {
        this.saving = false
      }
    },

    // 添加公司科目
    addCompanySubject(accModel) {
      // 这里可以打开一个对话框来选择公司和科目
      this.$message.info(`添加${accModel === 1 ? '税务核算' : '股东核算'}公司科目功能待实现`)
    },

    // 编辑公司科目
    editCompanySubjects(company, accModel) {
      // 这里可以打开一个对话框来编辑该公司的科目
      this.$message.info(`编辑${company.companyName}的${accModel === 1 ? '税务核算' : '股东核算'}科目功能待实现`)
    },

    // 移除公司科目
    async removeCompanySubjects(company, accModel) {
      try {
        await this.$confirm(`确定要移除${company.companyName}的${accModel === 1 ? '税务核算' : '股东核算'}科目吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await removeCertTemplTemp({
          templId: this.selectedTemplateId,
          companyId: company.companyId,
          accModel: accModel
        })

        this.$message.success('移除成功')
        await this.loadCertTemplTempList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('移除失败')
          console.error(error)
        }
      }
    }
  }
}
</script>

<style scoped>
.cert-subject-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 头部区域 */
.header-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.template-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selector-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

/* 主内容区域 */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.no-template-selected {
  background: white;
  border-radius: 8px;
  padding: 60px 20px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 核算分类 */
.accounting-category {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.category-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-title i {
  font-size: 20px;
  color: #409eff;
}

/* 公司科目网格 */
.company-subjects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.company-subject-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.company-subject-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.company-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.company-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.company-actions {
  display: flex;
  gap: 8px;
}

.subjects-list {
  min-height: 60px;
}

.subject-item {
  padding: 6px 12px;
  margin-bottom: 6px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-size: 14px;
  color: #606266;
}

.subject-item:last-child {
  margin-bottom: 0;
}

.subject-name {
  font-weight: 500;
}

.no-subjects {
  text-align: center;
  color: #c0c4cc;
  font-size: 14px;
  padding: 20px;
  font-style: italic;
}

.empty-category {
  grid-column: 1 / -1;
  padding: 40px;
  text-align: center;
}

/* 批量选择对话框 */
.batch-select-content {
  max-height: 600px;
  overflow-y: auto;
}

.select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.template-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  font-weight: 500;
  color: #606266;
}

.info-value {
  font-weight: 600;
  color: #303133;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.accounts-selection {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.selection-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.account-checkbox {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.account-checkbox:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.account-checkbox.is-checked {
  border-color: #409eff;
  background: #f0f9ff;
}

.account-info {
  margin-left: 8px;
}

.account-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.account-detail {
  font-size: 12px;
  color: #909399;
}
</style>
